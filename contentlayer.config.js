import { defineDocumentType, makeSource } from 'contentlayer/source-files'
import rehypeSlug from 'rehype-slug'
import rehypeAutolinkHeadings from 'rehype-autolink-headings'
import rehypeHighlight from 'rehype-highlight'

/** @type {import('contentlayer/source-files').ComputedFields} */
const computedFields = {
  slug: {
    type: 'string',
    resolve: (doc) => `/${doc._raw.flattenedPath}`,
  },
  slugAsParams: {
    type: 'string',
    resolve: (doc) => doc._raw.flattenedPath.split('/').slice(1).join('/'),
  },
  locale: {
    type: 'string',
    resolve: (doc) => doc._raw.flattenedPath.split('/')[0],
  },
  readingTime: {
    type: 'number',
    resolve: (doc) => {
      const wordsPerMinute = 200
      const wordCount = doc.body.raw.split(/\s+/).length
      return Math.ceil(wordCount / wordsPerMinute)
    },
  },
  wordCount: {
    type: 'number',
    resolve: (doc) => doc.body.raw.split(/\s+/).length,
  },
}

export const Product = defineDocumentType(() => ({
  name: 'Product',
  filePathPattern: `**/products/**/*.mdx`,
  contentType: 'mdx',
  fields: {
    title: {
      type: 'string',
      required: true,
      description: '产品名称',
    },
    description: {
      type: 'string',
      required: true,
      description: '产品简短描述',
    },
    category: {
      type: 'enum',
      options: ['barrier', 'gate', 'emergency', 'industrial', 'residential'],
      required: true,
      description: '产品分类',
    },
    featured: {
      type: 'boolean',
      default: false,
      description: '是否为特色产品',
    },
    price: {
      type: 'string',
      required: false,
      description: '价格信息',
    },
    priceRange: {
      type: 'json',
      required: false,
      description: '价格区间 {min: number, max: number, currency: string}',
    },
    specs: {
      type: 'json',
      required: false,
      description: '技术规格',
    },
    dimensions: {
      type: 'json',
      required: false,
      description: '尺寸信息 {height: string, width: string, length: string, weight: string}',
    },
    materials: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '材料列表',
    },
    certifications: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '认证列表',
    },
    images: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '产品图片路径列表',
    },
    gallery: {
      type: 'json',
      required: false,
      description: '图片画廊 {main: string, thumbnails: string[], videos: string[]}',
    },
    tags: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '标签列表',
    },
    applications: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '应用场景',
    },
    availability: {
      type: 'enum',
      options: ['in-stock', 'pre-order', 'out-of-stock', 'discontinued'],
      default: 'in-stock',
      description: '库存状态',
    },
    leadTime: {
      type: 'string',
      required: false,
      description: '交货周期',
    },
    warranty: {
      type: 'string',
      required: false,
      description: '保修信息',
    },
    publishedAt: {
      type: 'date',
      required: true,
      description: '发布日期',
    },
    updatedAt: {
      type: 'date',
      required: false,
      description: '更新日期',
    },
    seoTitle: {
      type: 'string',
      required: false,
      description: 'SEO标题',
    },
    seoDescription: {
      type: 'string',
      required: false,
      description: 'SEO描述',
    },
  },
  computedFields: {
    ...computedFields,
    categoryLabel: {
      type: 'string',
      resolve: (doc) => {
        const categoryLabels = {
          barrier: '防洪墙',
          gate: '防洪闸',
          emergency: '应急防洪',
          industrial: '工业防护',
          residential: '住宅防护'
        }
        return categoryLabels[doc.category] || doc.category
      },
    },
    isNew: {
      type: 'boolean',
      resolve: (doc) => {
        const publishDate = new Date(doc.publishedAt)
        const now = new Date()
        const daysDiff = (now.getTime() - publishDate.getTime()) / (1000 * 3600 * 24)
        return daysDiff <= 30 // 30天内为新产品
      },
    },
  },
}))

export const Page = defineDocumentType(() => ({
  name: 'Page',
  filePathPattern: `**/pages/**/*.mdx`,
  contentType: 'mdx',
  fields: {
    title: {
      type: 'string',
      required: true,
      description: '页面标题',
    },
    description: {
      type: 'string',
      required: true,
      description: '页面描述',
    },
    type: {
      type: 'enum',
      options: ['about', 'contact', 'services', 'legal', 'help'],
      required: false,
      description: '页面类型',
    },
    layout: {
      type: 'enum',
      options: ['default', 'full-width', 'sidebar', 'landing'],
      default: 'default',
      description: '页面布局',
    },
    hero: {
      type: 'json',
      required: false,
      description: '英雄区域配置 {title: string, subtitle: string, image: string, cta: object}',
    },
    sections: {
      type: 'json',
      required: false,
      description: '页面区块配置',
    },
    showInNavigation: {
      type: 'boolean',
      default: false,
      description: '是否在导航中显示',
    },
    navigationOrder: {
      type: 'number',
      required: false,
      description: '导航排序',
    },
    publishedAt: {
      type: 'date',
      required: true,
      description: '发布日期',
    },
    updatedAt: {
      type: 'date',
      required: false,
      description: '更新日期',
    },
    seoTitle: {
      type: 'string',
      required: false,
      description: 'SEO标题',
    },
    seoDescription: {
      type: 'string',
      required: false,
      description: 'SEO描述',
    },
  },
  computedFields,
}))

export const Blog = defineDocumentType(() => ({
  name: 'Blog',
  filePathPattern: `**/blog/**/*.mdx`,
  contentType: 'mdx',
  fields: {
    title: {
      type: 'string',
      required: true,
      description: '文章标题',
    },
    description: {
      type: 'string',
      required: true,
      description: '文章摘要',
    },
    author: {
      type: 'json',
      required: true,
      description: '作者信息 {name: string, avatar: string, bio: string}',
    },
    category: {
      type: 'enum',
      options: ['news', 'technology', 'case-study', 'industry', 'tutorial'],
      required: true,
      description: '文章分类',
    },
    publishedAt: {
      type: 'date',
      required: true,
      description: '发布日期',
    },
    updatedAt: {
      type: 'date',
      required: false,
      description: '更新日期',
    },
    featured: {
      type: 'boolean',
      default: false,
      description: '是否为特色文章',
    },
    tags: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '标签列表',
    },
    image: {
      type: 'string',
      required: false,
      description: '封面图片',
    },
    gallery: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '图片画廊',
    },
    relatedProducts: {
      type: 'list',
      of: { type: 'string' },
      required: false,
      description: '相关产品ID列表',
    },
    seoTitle: {
      type: 'string',
      required: false,
      description: 'SEO标题',
    },
    seoDescription: {
      type: 'string',
      required: false,
      description: 'SEO描述',
    },
  },
  computedFields: {
    ...computedFields,
    categoryLabel: {
      type: 'string',
      resolve: (doc) => {
        const categoryLabels = {
          news: '新闻动态',
          technology: '技术文章',
          'case-study': '案例研究',
          industry: '行业资讯',
          tutorial: '使用教程'
        }
        return categoryLabels[doc.category] || doc.category
      },
    },
  },
}))

export default makeSource({
  contentDirPath: './content',
  documentTypes: [Product, Page, Blog],
  disableImportAliasWarning: true,
  mdx: {
    remarkPlugins: [
      // 可以添加remark插件
    ],
    rehypePlugins: [
      rehypeSlug,
      [
        rehypeAutolinkHeadings,
        {
          behavior: 'wrap',
          properties: {
            className: ['anchor', 'group-hover:opacity-100', 'opacity-0', 'transition-opacity'],
            ariaLabel: '链接到此标题',
          },
          content: {
            type: 'element',
            tagName: 'span',
            properties: {
              className: ['anchor-icon'],
            },
            children: [
              {
                type: 'text',
                value: '#',
              },
            ],
          },
        },
      ],
      [
        rehypeHighlight,
        {
          theme: 'github-dark',
          ignoreMissing: true,
        },
      ],
    ],
  },
  onSuccess: async (importData) => {
    console.log('✅ Contentlayer build completed successfully!')

    if (importData && importData.allDocuments) {
      console.log(`📄 Generated ${importData.allDocuments.length} documents`)

      // 统计各类型文档数量
      const stats = importData.allDocuments.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1
        return acc
      }, {})

      console.log('📊 Document statistics:')
      Object.entries(stats).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`)
      })
    } else {
      console.log('📄 Build completed but no document data available')
    }
  },
  onUnknownDocuments: 'skip-warn',
})

// NOTE This file is auto-generated by Contentlayer

import type { Markdown, MDX, ImageFieldData, IsoDateTimeString } from 'contentlayer/core'
import * as Local from 'contentlayer/source-files'

export { isType } from 'contentlayer/client'

export type { Markdown, MDX, ImageFieldData, IsoDateTimeString }

/** Document types */
export type Blog = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Blog'
  /** 文章标题 */
  title: string
  /** 文章摘要 */
  description: string
  /** 作者信息 {name: string, avatar: string, bio: string} */
  author: any
  /** 文章分类 */
  category: 'news' | 'technology' | 'case-study' | 'industry' | 'tutorial'
  /** 发布日期 */
  publishedAt: IsoDateTimeString
  /** 更新日期 */
  updatedAt?: IsoDateTimeString | undefined
  /** 是否为特色文章 */
  featured: boolean
  /** 标签列表 */
  tags?: string[] | undefined
  /** 封面图片 */
  image?: string | undefined
  /** 图片画廊 */
  gallery?: string[] | undefined
  /** 相关产品ID列表 */
  relatedProducts?: string[] | undefined
  /** SEO标题 */
  seoTitle?: string | undefined
  /** SEO描述 */
  seoDescription?: string | undefined
  /** MDX file body */
  body: MDX
  slug: string
  slugAsParams: string
  locale: string
  readingTime: number
  wordCount: number
  categoryLabel: string
}

export type Page = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Page'
  /** 页面标题 */
  title: string
  /** 页面描述 */
  description: string
  /** 页面布局 */
  layout: 'default' | 'full-width' | 'sidebar' | 'landing'
  /** 英雄区域配置 {title: string, subtitle: string, image: string, cta: object} */
  hero?: any | undefined
  /** 页面区块配置 */
  sections?: any | undefined
  /** 是否在导航中显示 */
  showInNavigation: boolean
  /** 导航排序 */
  navigationOrder?: number | undefined
  /** 发布日期 */
  publishedAt: IsoDateTimeString
  /** 更新日期 */
  updatedAt?: IsoDateTimeString | undefined
  /** SEO标题 */
  seoTitle?: string | undefined
  /** SEO描述 */
  seoDescription?: string | undefined
  /** MDX file body */
  body: MDX
  slug: string
  slugAsParams: string
  locale: string
  readingTime: number
  wordCount: number
}

export type Product = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Product'
  /** 产品名称 */
  title: string
  /** 产品简短描述 */
  description: string
  /** 产品分类 */
  category: 'barrier' | 'gate' | 'emergency' | 'industrial' | 'residential'
  /** 是否为特色产品 */
  featured: boolean
  /** 价格信息 */
  price?: string | undefined
  /** 价格区间 {min: number, max: number, currency: string} */
  priceRange?: any | undefined
  /** 技术规格 */
  specs?: any | undefined
  /** 尺寸信息 {height: string, width: string, length: string, weight: string} */
  dimensions?: any | undefined
  /** 材料列表 */
  materials?: string[] | undefined
  /** 认证列表 */
  certifications?: string[] | undefined
  /** 产品图片路径列表 */
  images?: string[] | undefined
  /** 图片画廊 {main: string, thumbnails: string[], videos: string[]} */
  gallery?: any | undefined
  /** 标签列表 */
  tags?: string[] | undefined
  /** 应用场景 */
  applications?: string[] | undefined
  /** 库存状态 */
  availability: 'in-stock' | 'pre-order' | 'out-of-stock' | 'discontinued'
  /** 交货周期 */
  leadTime?: string | undefined
  /** 保修信息 */
  warranty?: string | undefined
  /** 发布日期 */
  publishedAt: IsoDateTimeString
  /** 更新日期 */
  updatedAt?: IsoDateTimeString | undefined
  /** SEO标题 */
  seoTitle?: string | undefined
  /** SEO描述 */
  seoDescription?: string | undefined
  /** MDX file body */
  body: MDX
  slug: string
  slugAsParams: string
  locale: string
  readingTime: number
  wordCount: number
  categoryLabel: string
  isNew: boolean
}  

/** Nested types */
  

/** Helper types */

export type AllTypes = DocumentTypes | NestedTypes
export type AllTypeNames = DocumentTypeNames | NestedTypeNames

export type DocumentTypes = Blog | Page | Product
export type DocumentTypeNames = 'Blog' | 'Page' | 'Product'

export type NestedTypes = never
export type NestedTypeNames = never

export type DataExports = {
  allDocuments: DocumentTypes[]
  allProducts: Product[]
  allPages: Page[]
  allBlogs: Blog[]
}


export interface ContentlayerGenTypes {
  documentTypes: DocumentTypes
  documentTypeMap: DocumentTypeMap
  documentTypeNames: DocumentTypeNames
  nestedTypes: NestedTypes
  nestedTypeMap: NestedTypeMap
  nestedTypeNames: NestedTypeNames
  allTypeNames: AllTypeNames
  dataExports: DataExports
}

declare global {
  interface ContentlayerGen extends ContentlayerGenTypes {}
}

export type DocumentTypeMap = {
  Blog: Blog
  Page: Page
  Product: Product
}

export type NestedTypeMap = {

}

 
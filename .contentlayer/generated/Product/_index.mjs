// NOTE This file is auto-generated by Contentlayer

import en__products__aquaDamMdx from './en__products__aqua-dam.mdx.json' assert { type: 'json' }
import en__products__floodGateMdx from './en__products__flood-gate.mdx.json' assert { type: 'json' }
import en__products__quickBarrierMdx from './en__products__quick-barrier.mdx.json' assert { type: 'json' }
import zhCn__products__aquaDamMdx from './zh-CN__products__aqua-dam.mdx.json' assert { type: 'json' }
import zhCn__products__floodGateMdx from './zh-CN__products__flood-gate.mdx.json' assert { type: 'json' }
import zhCn__products__quickBarrierMdx from './zh-CN__products__quick-barrier.mdx.json' assert { type: 'json' }

export const allProducts = [en__products__aquaDamMdx, en__products__floodGateMdx, en__products__quickBarrierMdx, zhCn__products__aquaDamMdx, zhCn__products__floodGateMdx, zhCn__products__quickBarrierMdx]

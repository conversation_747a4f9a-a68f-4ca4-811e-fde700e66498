{"version": 3, "sources": ["../../../contentlayer.config.js"], "sourcesContent": ["import { defineDocumentType, makeSource } from 'contentlayer/source-files'\nimport rehypeSlug from 'rehype-slug'\nimport rehypeAutolinkHeadings from 'rehype-autolink-headings'\nimport rehypeHighlight from 'rehype-highlight'\n\n/** @type {import('contentlayer/source-files').ComputedFields} */\nconst computedFields = {\n  slug: {\n    type: 'string',\n    resolve: (doc) => `/${doc._raw.flattenedPath}`,\n  },\n  slugAsParams: {\n    type: 'string',\n    resolve: (doc) => doc._raw.flattenedPath.split('/').slice(1).join('/'),\n  },\n  locale: {\n    type: 'string',\n    resolve: (doc) => doc._raw.flattenedPath.split('/')[0],\n  },\n  readingTime: {\n    type: 'number',\n    resolve: (doc) => {\n      const wordsPerMinute = 200\n      const wordCount = doc.body.raw.split(/\\s+/).length\n      return Math.ceil(wordCount / wordsPerMinute)\n    },\n  },\n  wordCount: {\n    type: 'number',\n    resolve: (doc) => doc.body.raw.split(/\\s+/).length,\n  },\n}\n\nexport const Product = defineDocumentType(() => ({\n  name: 'Product',\n  filePathPattern: `**/products/**/*.mdx`,\n  contentType: 'mdx',\n  fields: {\n    title: {\n      type: 'string',\n      required: true,\n      description: '产品名称',\n    },\n    description: {\n      type: 'string',\n      required: true,\n      description: '产品简短描述',\n    },\n    category: {\n      type: 'enum',\n      options: ['barrier', 'gate', 'emergency', 'industrial', 'residential'],\n      required: true,\n      description: '产品分类',\n    },\n    featured: {\n      type: 'boolean',\n      default: false,\n      description: '是否为特色产品',\n    },\n    price: {\n      type: 'string',\n      required: false,\n      description: '价格信息',\n    },\n    priceRange: {\n      type: 'json',\n      required: false,\n      description: '价格区间 {min: number, max: number, currency: string}',\n    },\n    specs: {\n      type: 'json',\n      required: false,\n      description: '技术规格',\n    },\n    dimensions: {\n      type: 'json',\n      required: false,\n      description: '尺寸信息 {height: string, width: string, length: string, weight: string}',\n    },\n    materials: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '材料列表',\n    },\n    certifications: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '认证列表',\n    },\n    images: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '产品图片路径列表',\n    },\n    gallery: {\n      type: 'json',\n      required: false,\n      description: '图片画廊 {main: string, thumbnails: string[], videos: string[]}',\n    },\n    tags: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '标签列表',\n    },\n    applications: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '应用场景',\n    },\n    availability: {\n      type: 'enum',\n      options: ['in-stock', 'pre-order', 'out-of-stock', 'discontinued'],\n      default: 'in-stock',\n      description: '库存状态',\n    },\n    leadTime: {\n      type: 'string',\n      required: false,\n      description: '交货周期',\n    },\n    warranty: {\n      type: 'string',\n      required: false,\n      description: '保修信息',\n    },\n    publishedAt: {\n      type: 'date',\n      required: true,\n      description: '发布日期',\n    },\n    updatedAt: {\n      type: 'date',\n      required: false,\n      description: '更新日期',\n    },\n    seoTitle: {\n      type: 'string',\n      required: false,\n      description: 'SEO标题',\n    },\n    seoDescription: {\n      type: 'string',\n      required: false,\n      description: 'SEO描述',\n    },\n  },\n  computedFields: {\n    ...computedFields,\n    categoryLabel: {\n      type: 'string',\n      resolve: (doc) => {\n        const categoryLabels = {\n          barrier: '防洪墙',\n          gate: '防洪闸',\n          emergency: '应急防洪',\n          industrial: '工业防护',\n          residential: '住宅防护'\n        }\n        return categoryLabels[doc.category] || doc.category\n      },\n    },\n    isNew: {\n      type: 'boolean',\n      resolve: (doc) => {\n        const publishDate = new Date(doc.publishedAt)\n        const now = new Date()\n        const daysDiff = (now.getTime() - publishDate.getTime()) / (1000 * 3600 * 24)\n        return daysDiff <= 30 // 30天内为新产品\n      },\n    },\n  },\n}))\n\nexport const Page = defineDocumentType(() => ({\n  name: 'Page',\n  filePathPattern: `**/pages/**/*.mdx`,\n  contentType: 'mdx',\n  fields: {\n    title: {\n      type: 'string',\n      required: true,\n      description: '页面标题',\n    },\n    description: {\n      type: 'string',\n      required: true,\n      description: '页面描述',\n    },\n    type: {\n      type: 'enum',\n      options: ['about', 'contact', 'services', 'legal', 'help'],\n      required: false,\n      description: '页面类型',\n    },\n    layout: {\n      type: 'enum',\n      options: ['default', 'full-width', 'sidebar', 'landing'],\n      default: 'default',\n      description: '页面布局',\n    },\n    hero: {\n      type: 'json',\n      required: false,\n      description: '英雄区域配置 {title: string, subtitle: string, image: string, cta: object}',\n    },\n    sections: {\n      type: 'json',\n      required: false,\n      description: '页面区块配置',\n    },\n    showInNavigation: {\n      type: 'boolean',\n      default: false,\n      description: '是否在导航中显示',\n    },\n    navigationOrder: {\n      type: 'number',\n      required: false,\n      description: '导航排序',\n    },\n    publishedAt: {\n      type: 'date',\n      required: true,\n      description: '发布日期',\n    },\n    updatedAt: {\n      type: 'date',\n      required: false,\n      description: '更新日期',\n    },\n    seoTitle: {\n      type: 'string',\n      required: false,\n      description: 'SEO标题',\n    },\n    seoDescription: {\n      type: 'string',\n      required: false,\n      description: 'SEO描述',\n    },\n  },\n  computedFields,\n}))\n\nexport const Blog = defineDocumentType(() => ({\n  name: 'Blog',\n  filePathPattern: `**/blog/**/*.mdx`,\n  contentType: 'mdx',\n  fields: {\n    title: {\n      type: 'string',\n      required: true,\n      description: '文章标题',\n    },\n    description: {\n      type: 'string',\n      required: true,\n      description: '文章摘要',\n    },\n    author: {\n      type: 'json',\n      required: true,\n      description: '作者信息 {name: string, avatar: string, bio: string}',\n    },\n    category: {\n      type: 'enum',\n      options: ['news', 'technology', 'case-study', 'industry', 'tutorial'],\n      required: true,\n      description: '文章分类',\n    },\n    publishedAt: {\n      type: 'date',\n      required: true,\n      description: '发布日期',\n    },\n    updatedAt: {\n      type: 'date',\n      required: false,\n      description: '更新日期',\n    },\n    featured: {\n      type: 'boolean',\n      default: false,\n      description: '是否为特色文章',\n    },\n    tags: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '标签列表',\n    },\n    image: {\n      type: 'string',\n      required: false,\n      description: '封面图片',\n    },\n    gallery: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '图片画廊',\n    },\n    relatedProducts: {\n      type: 'list',\n      of: { type: 'string' },\n      required: false,\n      description: '相关产品ID列表',\n    },\n    seoTitle: {\n      type: 'string',\n      required: false,\n      description: 'SEO标题',\n    },\n    seoDescription: {\n      type: 'string',\n      required: false,\n      description: 'SEO描述',\n    },\n  },\n  computedFields: {\n    ...computedFields,\n    categoryLabel: {\n      type: 'string',\n      resolve: (doc) => {\n        const categoryLabels = {\n          news: '新闻动态',\n          technology: '技术文章',\n          'case-study': '案例研究',\n          industry: '行业资讯',\n          tutorial: '使用教程'\n        }\n        return categoryLabels[doc.category] || doc.category\n      },\n    },\n  },\n}))\n\nexport default makeSource({\n  contentDirPath: './content',\n  documentTypes: [Product, Page, Blog],\n  disableImportAliasWarning: true,\n  mdx: {\n    remarkPlugins: [\n      // 可以添加remark插件\n    ],\n    rehypePlugins: [\n      rehypeSlug,\n      [\n        rehypeAutolinkHeadings,\n        {\n          behavior: 'wrap',\n          properties: {\n            className: ['anchor', 'group-hover:opacity-100', 'opacity-0', 'transition-opacity'],\n            ariaLabel: '链接到此标题',\n          },\n          content: {\n            type: 'element',\n            tagName: 'span',\n            properties: {\n              className: ['anchor-icon'],\n            },\n            children: [\n              {\n                type: 'text',\n                value: '#',\n              },\n            ],\n          },\n        },\n      ],\n      [\n        rehypeHighlight,\n        {\n          theme: 'github-dark',\n          ignoreMissing: true,\n        },\n      ],\n    ],\n  },\n  onSuccess: async (importData) => {\n    console.log('✅ Contentlayer build completed successfully!')\n\n    if (importData && importData.allDocuments) {\n      console.log(`📄 Generated ${importData.allDocuments.length} documents`)\n\n      // 统计各类型文档数量\n      const stats = importData.allDocuments.reduce((acc, doc) => {\n        acc[doc.type] = (acc[doc.type] || 0) + 1\n        return acc\n      }, {})\n\n      console.log('📊 Document statistics:')\n      Object.entries(stats).forEach(([type, count]) => {\n        console.log(`   ${type}: ${count}`)\n      })\n    } else {\n      console.log('📄 Build completed but no document data available')\n    }\n  },\n  onUnknownDocuments: 'skip-warn',\n})\n"], "mappings": ";AAAA,SAAS,oBAAoB,kBAAkB;AAC/C,OAAO,gBAAgB;AACvB,OAAO,4BAA4B;AACnC,OAAO,qBAAqB;AAG5B,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,IAAI,IAAI,KAAK,aAAa;AAAA,EAC9C;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,EACvE;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,CAAC;AAAA,EACvD;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ;AAChB,YAAM,iBAAiB;AACvB,YAAM,YAAY,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE;AAC5C,aAAO,KAAK,KAAK,YAAY,cAAc;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE;AAAA,EAC9C;AACF;AAEO,IAAM,UAAU,mBAAmB,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,CAAC,WAAW,QAAQ,aAAa,cAAc,aAAa;AAAA,MACrE,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,CAAC,YAAY,aAAa,gBAAgB,cAAc;AAAA,MACjE,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,GAAG;AAAA,IACH,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,iBAAiB;AAAA,UACrB,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AACA,eAAO,eAAe,IAAI,QAAQ,KAAK,IAAI;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,cAAc,IAAI,KAAK,IAAI,WAAW;AAC5C,cAAM,MAAM,oBAAI,KAAK;AACrB,cAAM,YAAY,IAAI,QAAQ,IAAI,YAAY,QAAQ,MAAM,MAAO,OAAO;AAC1E,eAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEK,IAAM,OAAO,mBAAmB,OAAO;AAAA,EAC5C,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,SAAS,WAAW,YAAY,SAAS,MAAM;AAAA,MACzD,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,WAAW,cAAc,WAAW,SAAS;AAAA,MACvD,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA;AACF,EAAE;AAEK,IAAM,OAAO,mBAAmB,OAAO;AAAA,EAC5C,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ,cAAc,cAAc,YAAY,UAAU;AAAA,MACpE,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,IAAI,EAAE,MAAM,SAAS;AAAA,MACrB,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,GAAG;AAAA,IACH,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,cAAM,iBAAiB;AAAA,UACrB,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AACA,eAAO,eAAe,IAAI,QAAQ,KAAK,IAAI;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAO,8BAAQ,WAAW;AAAA,EACxB,gBAAgB;AAAA,EAChB,eAAe,CAAC,SAAS,MAAM,IAAI;AAAA,EACnC,2BAA2B;AAAA,EAC3B,KAAK;AAAA,IACH,eAAe;AAAA;AAAA,IAEf;AAAA,IACA,eAAe;AAAA,MACb;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,YACV,WAAW,CAAC,UAAU,2BAA2B,aAAa,oBAAoB;AAAA,YAClF,WAAW;AAAA,UACb;AAAA,UACA,SAAS;AAAA,YACP,MAAM;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,cACV,WAAW,CAAC,aAAa;AAAA,YAC3B;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO,eAAe;AAC/B,YAAQ,IAAI,mDAA8C;AAE1D,QAAI,cAAc,WAAW,cAAc;AACzC,cAAQ,IAAI,uBAAgB,WAAW,aAAa,MAAM,YAAY;AAGtE,YAAM,QAAQ,WAAW,aAAa,OAAO,CAAC,KAAK,QAAQ;AACzD,YAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AACvC,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,cAAQ,IAAI,gCAAyB;AACrC,aAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AAC/C,gBAAQ,IAAI,MAAM,IAAI,KAAK,KAAK,EAAE;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,IAAI,0DAAmD;AAAA,IACjE;AAAA,EACF;AAAA,EACA,oBAAoB;AACtB,CAAC;", "names": []}
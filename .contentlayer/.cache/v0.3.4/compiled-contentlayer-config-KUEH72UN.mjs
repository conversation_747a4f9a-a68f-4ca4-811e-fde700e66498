// contentlayer.config.js
import { defineDocumentType, makeSource } from "contentlayer/source-files";
import rehypeSlug from "rehype-slug";
import rehypeAutolinkHeadings from "rehype-autolink-headings";
import rehypeHighlight from "rehype-highlight";
var computedFields = {
  slug: {
    type: "string",
    resolve: (doc) => `/${doc._raw.flattenedPath}`
  },
  slugAsParams: {
    type: "string",
    resolve: (doc) => doc._raw.flattenedPath.split("/").slice(1).join("/")
  },
  locale: {
    type: "string",
    resolve: (doc) => doc._raw.flattenedPath.split("/")[0]
  },
  readingTime: {
    type: "number",
    resolve: (doc) => {
      const wordsPerMinute = 200;
      const wordCount = doc.body.raw.split(/\s+/).length;
      return Math.ceil(wordCount / wordsPerMinute);
    }
  },
  wordCount: {
    type: "number",
    resolve: (doc) => doc.body.raw.split(/\s+/).length
  }
};
var Product = defineDocumentType(() => ({
  name: "Product",
  filePathPattern: `**/products/**/*.mdx`,
  contentType: "mdx",
  fields: {
    title: {
      type: "string",
      required: true,
      description: "\u4EA7\u54C1\u540D\u79F0"
    },
    description: {
      type: "string",
      required: true,
      description: "\u4EA7\u54C1\u7B80\u77ED\u63CF\u8FF0"
    },
    category: {
      type: "enum",
      options: ["barrier", "gate", "emergency", "industrial", "residential"],
      required: true,
      description: "\u4EA7\u54C1\u5206\u7C7B"
    },
    featured: {
      type: "boolean",
      default: false,
      description: "\u662F\u5426\u4E3A\u7279\u8272\u4EA7\u54C1"
    },
    price: {
      type: "string",
      required: false,
      description: "\u4EF7\u683C\u4FE1\u606F"
    },
    priceRange: {
      type: "json",
      required: false,
      description: "\u4EF7\u683C\u533A\u95F4 {min: number, max: number, currency: string}"
    },
    specs: {
      type: "json",
      required: false,
      description: "\u6280\u672F\u89C4\u683C"
    },
    dimensions: {
      type: "json",
      required: false,
      description: "\u5C3A\u5BF8\u4FE1\u606F {height: string, width: string, length: string, weight: string}"
    },
    materials: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u6750\u6599\u5217\u8868"
    },
    certifications: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u8BA4\u8BC1\u5217\u8868"
    },
    images: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u4EA7\u54C1\u56FE\u7247\u8DEF\u5F84\u5217\u8868"
    },
    gallery: {
      type: "json",
      required: false,
      description: "\u56FE\u7247\u753B\u5ECA {main: string, thumbnails: string[], videos: string[]}"
    },
    tags: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u6807\u7B7E\u5217\u8868"
    },
    applications: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u5E94\u7528\u573A\u666F"
    },
    availability: {
      type: "enum",
      options: ["in-stock", "pre-order", "out-of-stock", "discontinued"],
      default: "in-stock",
      description: "\u5E93\u5B58\u72B6\u6001"
    },
    leadTime: {
      type: "string",
      required: false,
      description: "\u4EA4\u8D27\u5468\u671F"
    },
    warranty: {
      type: "string",
      required: false,
      description: "\u4FDD\u4FEE\u4FE1\u606F"
    },
    publishedAt: {
      type: "date",
      required: true,
      description: "\u53D1\u5E03\u65E5\u671F"
    },
    updatedAt: {
      type: "date",
      required: false,
      description: "\u66F4\u65B0\u65E5\u671F"
    },
    seoTitle: {
      type: "string",
      required: false,
      description: "SEO\u6807\u9898"
    },
    seoDescription: {
      type: "string",
      required: false,
      description: "SEO\u63CF\u8FF0"
    }
  },
  computedFields: {
    ...computedFields,
    categoryLabel: {
      type: "string",
      resolve: (doc) => {
        const categoryLabels = {
          barrier: "\u9632\u6D2A\u5899",
          gate: "\u9632\u6D2A\u95F8",
          emergency: "\u5E94\u6025\u9632\u6D2A",
          industrial: "\u5DE5\u4E1A\u9632\u62A4",
          residential: "\u4F4F\u5B85\u9632\u62A4"
        };
        return categoryLabels[doc.category] || doc.category;
      }
    },
    isNew: {
      type: "boolean",
      resolve: (doc) => {
        const publishDate = new Date(doc.publishedAt);
        const now = /* @__PURE__ */ new Date();
        const daysDiff = (now.getTime() - publishDate.getTime()) / (1e3 * 3600 * 24);
        return daysDiff <= 30;
      }
    }
  }
}));
var Page = defineDocumentType(() => ({
  name: "Page",
  filePathPattern: `**/pages/**/*.mdx`,
  contentType: "mdx",
  fields: {
    title: {
      type: "string",
      required: true,
      description: "\u9875\u9762\u6807\u9898"
    },
    description: {
      type: "string",
      required: true,
      description: "\u9875\u9762\u63CF\u8FF0"
    },
    type: {
      type: "enum",
      options: ["about", "contact", "services", "legal", "help"],
      required: false,
      description: "\u9875\u9762\u7C7B\u578B"
    },
    layout: {
      type: "enum",
      options: ["default", "full-width", "sidebar", "landing"],
      default: "default",
      description: "\u9875\u9762\u5E03\u5C40"
    },
    hero: {
      type: "json",
      required: false,
      description: "\u82F1\u96C4\u533A\u57DF\u914D\u7F6E {title: string, subtitle: string, image: string, cta: object}"
    },
    sections: {
      type: "json",
      required: false,
      description: "\u9875\u9762\u533A\u5757\u914D\u7F6E"
    },
    showInNavigation: {
      type: "boolean",
      default: false,
      description: "\u662F\u5426\u5728\u5BFC\u822A\u4E2D\u663E\u793A"
    },
    navigationOrder: {
      type: "number",
      required: false,
      description: "\u5BFC\u822A\u6392\u5E8F"
    },
    publishedAt: {
      type: "date",
      required: true,
      description: "\u53D1\u5E03\u65E5\u671F"
    },
    updatedAt: {
      type: "date",
      required: false,
      description: "\u66F4\u65B0\u65E5\u671F"
    },
    seoTitle: {
      type: "string",
      required: false,
      description: "SEO\u6807\u9898"
    },
    seoDescription: {
      type: "string",
      required: false,
      description: "SEO\u63CF\u8FF0"
    }
  },
  computedFields
}));
var Blog = defineDocumentType(() => ({
  name: "Blog",
  filePathPattern: `**/blog/**/*.mdx`,
  contentType: "mdx",
  fields: {
    title: {
      type: "string",
      required: true,
      description: "\u6587\u7AE0\u6807\u9898"
    },
    description: {
      type: "string",
      required: true,
      description: "\u6587\u7AE0\u6458\u8981"
    },
    author: {
      type: "json",
      required: true,
      description: "\u4F5C\u8005\u4FE1\u606F {name: string, avatar: string, bio: string}"
    },
    category: {
      type: "enum",
      options: ["news", "technology", "case-study", "industry", "tutorial"],
      required: true,
      description: "\u6587\u7AE0\u5206\u7C7B"
    },
    publishedAt: {
      type: "date",
      required: true,
      description: "\u53D1\u5E03\u65E5\u671F"
    },
    updatedAt: {
      type: "date",
      required: false,
      description: "\u66F4\u65B0\u65E5\u671F"
    },
    featured: {
      type: "boolean",
      default: false,
      description: "\u662F\u5426\u4E3A\u7279\u8272\u6587\u7AE0"
    },
    tags: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u6807\u7B7E\u5217\u8868"
    },
    image: {
      type: "string",
      required: false,
      description: "\u5C01\u9762\u56FE\u7247"
    },
    gallery: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u56FE\u7247\u753B\u5ECA"
    },
    relatedProducts: {
      type: "list",
      of: { type: "string" },
      required: false,
      description: "\u76F8\u5173\u4EA7\u54C1ID\u5217\u8868"
    },
    seoTitle: {
      type: "string",
      required: false,
      description: "SEO\u6807\u9898"
    },
    seoDescription: {
      type: "string",
      required: false,
      description: "SEO\u63CF\u8FF0"
    }
  },
  computedFields: {
    ...computedFields,
    categoryLabel: {
      type: "string",
      resolve: (doc) => {
        const categoryLabels = {
          news: "\u65B0\u95FB\u52A8\u6001",
          technology: "\u6280\u672F\u6587\u7AE0",
          "case-study": "\u6848\u4F8B\u7814\u7A76",
          industry: "\u884C\u4E1A\u8D44\u8BAF",
          tutorial: "\u4F7F\u7528\u6559\u7A0B"
        };
        return categoryLabels[doc.category] || doc.category;
      }
    }
  }
}));
var contentlayer_config_default = makeSource({
  contentDirPath: "./content",
  documentTypes: [Product, Page, Blog],
  disableImportAliasWarning: true,
  mdx: {
    remarkPlugins: [
      // 可以添加remark插件
    ],
    rehypePlugins: [
      rehypeSlug,
      [
        rehypeAutolinkHeadings,
        {
          behavior: "wrap",
          properties: {
            className: ["anchor", "group-hover:opacity-100", "opacity-0", "transition-opacity"],
            ariaLabel: "\u94FE\u63A5\u5230\u6B64\u6807\u9898"
          },
          content: {
            type: "element",
            tagName: "span",
            properties: {
              className: ["anchor-icon"]
            },
            children: [
              {
                type: "text",
                value: "#"
              }
            ]
          }
        }
      ],
      [
        rehypeHighlight,
        {
          theme: "github-dark",
          ignoreMissing: true
        }
      ]
    ]
  },
  onSuccess: async (importData) => {
    console.log("\u2705 Contentlayer build completed successfully!");
    if (importData && importData.allDocuments) {
      console.log(`\u{1F4C4} Generated ${importData.allDocuments.length} documents`);
      const stats = importData.allDocuments.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1;
        return acc;
      }, {});
      console.log("\u{1F4CA} Document statistics:");
      Object.entries(stats).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`);
      });
    } else {
      console.log("\u{1F4C4} Build completed but no document data available");
    }
  },
  onUnknownDocuments: "skip-warn"
});
export {
  Blog,
  Page,
  Product,
  contentlayer_config_default as default
};
//# sourceMappingURL=compiled-contentlayer-config-KUEH72UN.mjs.map

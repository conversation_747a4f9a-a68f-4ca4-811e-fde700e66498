/** @type {import('next-i18next').UserConfig} */
module.exports = {
  // 调试模式（开发环境启用）
  debug: process.env.NODE_ENV === 'development',
  
  // 支持的语言列表
  i18n: {
    defaultLocale: 'zh-C<PERSON>',
    locales: ['en', 'zh-CN', 'ja', 'es'],
    localeDetection: false, // 禁用自动语言检测，使用URL路径
  },

  // 命名空间配置
  ns: [
    'common',      // 通用文本
    'navigation',  // 导航菜单
    'home',        // 首页
    'products',    // 产品页面
    'about',       // 关于我们
    'contact',     // 联系我们
    'footer',      // 页脚
    'forms',       // 表单
    'errors',      // 错误信息
    'seo',         // SEO相关
  ],

  // 默认命名空间
  defaultNS: 'common',

  // 回退语言配置
  fallbackLng: {
    'zh-CN': ['zh', 'en'],
    'zh-TW': ['zh-CN', 'zh', 'en'],
    'zh-HK': ['zh-CN', 'zh', 'en'],
    'ja': ['en'],
    'es': ['en'],
    'default': ['en']
  },

  // 非显式支持的语言回退到英语
  nonExplicitSupportedLngs: true,

  // 加载路径配置
  localePath: './locales',
  
  // 资源加载配置
  load: 'languageOnly',
  
  // 预加载语言
  preload: ['en', 'zh-CN'],

  // 键分隔符和命名空间分隔符
  keySeparator: '.',
  nsSeparator: ':',

  // 插值配置
  interpolation: {
    escapeValue: false, // React已经处理了XSS
    formatSeparator: ',',
    format: function(value, format, lng) {
      if (format === 'uppercase') return value.toUpperCase()
      if (format === 'lowercase') return value.toLowerCase()
      if (format === 'currency') {
        const currencyMap = {
          'en': 'USD',
          'zh-CN': 'CNY',
          'ja': 'JPY',
          'es': 'EUR'
        }
        const currency = currencyMap[lng] || 'USD'
        return new Intl.NumberFormat(lng, {
          style: 'currency',
          currency: currency
        }).format(value)
      }
      return value
    }
  },

  // 复数规则配置
  pluralSeparator: '_',
  contextSeparator: '_',

  // 服务端渲染配置
  react: {
    useSuspense: false, // 禁用Suspense以避免SSR问题
    bindI18n: 'languageChanged',
    bindI18nStore: '',
    transEmptyNodeValue: '',
    transSupportBasicHtmlNodes: true,
    transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span'],
  },

  // 检测配置
  detection: {
    order: ['path', 'header', 'cookie', 'localStorage', 'subdomain', 'querystring'],
    caches: ['localStorage', 'cookie'],
    excludeCacheFor: ['cimode'],
    cookieMinutes: 60 * 24 * 30, // 30天
    cookieDomain: process.env.NODE_ENV === 'production' ? '.tucsenberg.com' : 'localhost',
    cookieOptions: {
      httpOnly: false,
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    }
  },

  // 后端配置
  backend: {
    loadPath: './locales/{{lng}}/{{ns}}.json',
    addPath: './locales/{{lng}}/{{ns}}.missing.json',
    allowMultiLoading: false,
    crossDomain: false,
    withCredentials: false,
    requestOptions: {
      mode: 'cors',
      credentials: 'same-origin',
      cache: 'default'
    }
  },

  // 缓存配置
  cache: {
    enabled: true,
    prefix: 'i18next_res_',
    expirationTime: 7 * 24 * 60 * 60 * 1000, // 7天
    versions: {}
  },

  // 开发环境配置
  saveMissing: process.env.NODE_ENV === 'development',
  saveMissingTo: 'current',
  missingKeyHandler: process.env.NODE_ENV === 'development' ? 
    function(lng, ns, key, fallbackValue) {
      console.warn(`Missing translation key: ${ns}:${key} for language: ${lng}`)
    } : undefined,

  // 更新缺失的键
  updateMissing: process.env.NODE_ENV === 'development',

  // 返回对象配置
  returnObjects: false,
  returnEmptyString: true,
  returnNull: true,

  // 语言标签配置
  cleanCode: true,
  lowerCaseLng: false,

  // 自定义格式化函数
  customFormat: function(value, format, lng, options) {
    // 日期格式化
    if (format === 'date') {
      return new Intl.DateTimeFormat(lng).format(new Date(value))
    }
    
    // 相对时间格式化
    if (format === 'relative') {
      const rtf = new Intl.RelativeTimeFormat(lng, { numeric: 'auto' })
      const diff = Math.floor((new Date(value) - new Date()) / (1000 * 60 * 60 * 24))
      return rtf.format(diff, 'day')
    }
    
    return value
  },

  // 语言特定配置
  resources: {},

  // 初始化选项
  initImmediate: false,
  
  // 语言变更时的回调
  onLanguageChange: function(lng) {
    if (typeof window !== 'undefined') {
      document.documentElement.lang = lng
      // 更新页面方向（如果需要支持RTL语言）
      document.documentElement.dir = ['ar', 'he', 'fa'].includes(lng) ? 'rtl' : 'ltr'
    }
  }
}

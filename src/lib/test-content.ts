// 测试内容工具函数
import { ProductUtils, ContentUtils } from './content'

export function testContentUtils() {
  console.log('🧪 Testing Content Utils...')
  
  try {
    // 测试获取所有产品分类
    const categories = ProductUtils.getCategories('zh-CN')
    console.log('📂 Categories (zh-CN):', categories)
    
    const categoriesEn = ProductUtils.getCategories('en')
    console.log('📂 Categories (en):', categoriesEn)
    
    // 测试获取特色产品
    const featuredProducts = ProductUtils.getFeatured('zh-CN')
    console.log('⭐ Featured Products (zh-CN):', featuredProducts.length)
    
    // 测试按分类获取产品
    const barrierProducts = ProductUtils.getByCategory('barrier', 'zh-CN')
    console.log('🚧 Barrier Products (zh-CN):', barrierProducts.length)
    
    // 测试搜索功能
    const searchResults = ProductUtils.search('防洪', 'zh-CN')
    console.log('🔍 Search Results for "防洪":', searchResults.length)
    
    // 测试获取支持的语言
    const locales = ContentUtils.getSupportedLocales()
    console.log('🌍 Supported Locales:', locales)
    
    // 测试全局搜索
    const globalSearch = ContentUtils.search('flood', 'en')
    console.log('🔍 Global Search Results for "flood":', globalSearch.length)
    
    console.log('✅ All tests passed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'

// 支持的语言配置
export const SUPPORTED_LOCALES = {
  'zh-CN': {
    name: '中文',
    nativeName: '简体中文',
    flag: '🇨🇳',
    dir: 'ltr',
    currency: 'CNY',
    dateFormat: 'YYYY年MM月DD日',
    numberFormat: 'zh-CN'
  },
  'en': {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
    currency: 'USD',
    dateFormat: 'MMMM DD, YYYY',
    numberFormat: 'en-US'
  },
  'ja': {
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
    currency: 'JPY',
    dateFormat: 'YYYY年MM月DD日',
    numberFormat: 'ja-JP'
  },
  'es': {
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
    currency: 'EUR',
    dateFormat: 'DD de MMMM de YYYY',
    numberFormat: 'es-ES'
  }
} as const

export type SupportedLocale = keyof typeof SUPPORTED_LOCALES

// 默认语言
export const DEFAULT_LOCALE: SupportedLocale = 'zh-CN'

// 语言检测和切换工具
export class I18nUtils {
  /**
   * 获取当前语言信息
   */
  static getCurrentLocale(): SupportedLocale {
    if (typeof window === 'undefined') return DEFAULT_LOCALE
    
    const router = useRouter()
    return (router.locale as SupportedLocale) || DEFAULT_LOCALE
  }

  /**
   * 获取语言配置
   */
  static getLocaleConfig(locale: SupportedLocale) {
    return SUPPORTED_LOCALES[locale] || SUPPORTED_LOCALES[DEFAULT_LOCALE]
  }

  /**
   * 获取所有支持的语言
   */
  static getSupportedLocales() {
    return Object.keys(SUPPORTED_LOCALES) as SupportedLocale[]
  }

  /**
   * 检查是否为支持的语言
   */
  static isSupportedLocale(locale: string): locale is SupportedLocale {
    return locale in SUPPORTED_LOCALES
  }

  /**
   * 切换语言
   */
  static switchLocale(locale: SupportedLocale, asPath?: string) {
    if (typeof window === 'undefined') return
    
    const router = useRouter()
    const path = asPath || router.asPath
    
    router.push(path, path, { locale })
  }

  /**
   * 获取本地化的URL
   */
  static getLocalizedUrl(path: string, locale: SupportedLocale) {
    if (locale === DEFAULT_LOCALE) {
      return path
    }
    return `/${locale}${path}`
  }

  /**
   * 从URL中提取语言
   */
  static extractLocaleFromPath(path: string): { locale: SupportedLocale; cleanPath: string } {
    const segments = path.split('/').filter(Boolean)
    const firstSegment = segments[0]
    
    if (this.isSupportedLocale(firstSegment)) {
      return {
        locale: firstSegment,
        cleanPath: '/' + segments.slice(1).join('/')
      }
    }
    
    return {
      locale: DEFAULT_LOCALE,
      cleanPath: path
    }
  }
}

// 格式化工具
export class FormatUtils {
  /**
   * 格式化货币
   */
  static formatCurrency(
    amount: number, 
    locale: SupportedLocale = DEFAULT_LOCALE,
    currency?: string
  ): string {
    const config = SUPPORTED_LOCALES[locale]
    const currencyCode = currency || config.currency
    
    return new Intl.NumberFormat(config.numberFormat, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  /**
   * 格式化数字
   */
  static formatNumber(
    number: number,
    locale: SupportedLocale = DEFAULT_LOCALE,
    options?: Intl.NumberFormatOptions
  ): string {
    const config = SUPPORTED_LOCALES[locale]
    
    return new Intl.NumberFormat(config.numberFormat, options).format(number)
  }

  /**
   * 格式化日期
   */
  static formatDate(
    date: Date | string,
    locale: SupportedLocale = DEFAULT_LOCALE,
    options?: Intl.DateTimeFormatOptions
  ): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const config = SUPPORTED_LOCALES[locale]
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
    
    return new Intl.DateTimeFormat(config.numberFormat, {
      ...defaultOptions,
      ...options
    }).format(dateObj)
  }

  /**
   * 格式化相对时间
   */
  static formatRelativeTime(
    date: Date | string,
    locale: SupportedLocale = DEFAULT_LOCALE
  ): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
    
    const config = SUPPORTED_LOCALES[locale]
    const rtf = new Intl.RelativeTimeFormat(config.numberFormat, { numeric: 'auto' })
    
    // 计算时间差
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
    }
  }
}

// 翻译工具Hook
export function useI18n() {
  const { t, i18n } = useTranslation()
  const router = useRouter()
  
  const currentLocale = (router.locale as SupportedLocale) || DEFAULT_LOCALE
  const localeConfig = SUPPORTED_LOCALES[currentLocale]
  
  return {
    t,
    i18n,
    currentLocale,
    localeConfig,
    switchLocale: (locale: SupportedLocale) => {
      I18nUtils.switchLocale(locale, router.asPath)
    },
    formatCurrency: (amount: number, currency?: string) => 
      FormatUtils.formatCurrency(amount, currentLocale, currency),
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) =>
      FormatUtils.formatNumber(number, currentLocale, options),
    formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions) =>
      FormatUtils.formatDate(date, currentLocale, options),
    formatRelativeTime: (date: Date | string) =>
      FormatUtils.formatRelativeTime(date, currentLocale)
  }
}

// 语言切换组件数据
export function getLanguageSwitcherData(currentLocale: SupportedLocale) {
  return Object.entries(SUPPORTED_LOCALES).map(([code, config]) => ({
    code: code as SupportedLocale,
    name: config.name,
    nativeName: config.nativeName,
    flag: config.flag,
    active: code === currentLocale
  }))
}

// SEO相关的语言工具
export class SEOUtils {
  /**
   * 生成hreflang标签
   */
  static generateHrefLangTags(path: string) {
    return Object.keys(SUPPORTED_LOCALES).map(locale => ({
      hrefLang: locale === 'zh-CN' ? 'zh-Hans' : locale,
      href: I18nUtils.getLocalizedUrl(path, locale as SupportedLocale)
    }))
  }

  /**
   * 获取本地化的元数据
   */
  static getLocalizedMetadata(
    baseMetadata: {
      title: string
      description: string
      keywords?: string
    },
    locale: SupportedLocale
  ) {
    const config = SUPPORTED_LOCALES[locale]
    
    return {
      ...baseMetadata,
      language: locale,
      locale: locale,
      alternateLanguages: Object.keys(SUPPORTED_LOCALES).reduce((acc, loc) => {
        acc[loc] = I18nUtils.getLocalizedUrl('/', loc as SupportedLocale)
        return acc
      }, {} as Record<string, string>)
    }
  }
}

// 导出常用类型和常量
export type LocaleConfig = typeof SUPPORTED_LOCALES[SupportedLocale]
export { SUPPORTED_LOCALES as LOCALES }

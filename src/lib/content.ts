import { allProducts } from 'contentlayer/generated'
import type { Product } from '@/types/content'

export class ProductUtils {
  static getByCategory(category: string, locale = 'zh-CN'): Product[] {
    return allProducts.filter(
      product => product.category === category && product.locale === locale
    ).sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
  }

  static getFeatured(locale = 'zh-CN', limit = 6): Product[] {
    return allProducts
      .filter(product => product.featured && product.locale === locale)
      .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
      .slice(0, limit)
  }

  static getCategories(locale = 'zh-CN'): string[] {
    const categories = new Set(
      allProducts
        .filter(product => product.locale === locale)
        .map(product => product.category)
    )
    return Array.from(categories)
  }

  static search(query: string, locale = 'zh-CN'): Product[] {
    const searchTerm = query.toLowerCase()
    return allProducts
      .filter(product => 
        product.locale === locale &&
        (product.title.toLowerCase().includes(searchTerm) ||
         product.description.toLowerCase().includes(searchTerm) ||
         product.tags?.some(tag => tag.toLowerCase().includes(searchTerm)))
      )
  }
}

// 从Contentlayer生成的类型导入
import type { Product, Page, Blog } from 'contentlayer/generated'

// 扩展产品类型
export interface ProductMeta extends Product {
  // 添加运行时计算的字段
  relatedProducts?: Product[]
  similarProducts?: Product[]
  reviews?: ProductReview[]
  averageRating?: number
}

// 产品评价类型
export interface ProductReview {
  id: string
  author: string
  rating: number
  comment: string
  date: string
  verified: boolean
}

// 扩展页面类型
export interface PageMeta extends Page {
  // 添加运行时计算的字段
  breadcrumbs?: Breadcrumb[]
  tableOfContents?: TableOfContentsItem[]
}

// 面包屑导航类型
export interface Breadcrumb {
  label: string
  href: string
  current?: boolean
}

// 目录项类型
export interface TableOfContentsItem {
  id: string
  title: string
  level: number
  children?: TableOfContentsItem[]
}

// 扩展博客类型
export interface BlogMeta extends Blog {
  // 添加运行时计算的字段
  relatedPosts?: Blog[]
  nextPost?: Blog
  previousPost?: Blog
  tableOfContents?: TableOfContentsItem[]
}

// 内容搜索结果类型
export interface SearchResult {
  type: 'product' | 'page' | 'blog'
  id: string
  title: string
  description: string
  url: string
  locale: string
  score: number
  highlights?: string[]
}

// 内容过滤器类型
export interface ContentFilter {
  type?: ('product' | 'page' | 'blog')[]
  locale?: string
  category?: string
  tags?: string[]
  featured?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
}

// 内容排序选项
export type ContentSortOption = 
  | 'publishedAt-desc'
  | 'publishedAt-asc'
  | 'updatedAt-desc'
  | 'updatedAt-asc'
  | 'title-asc'
  | 'title-desc'
  | 'featured'

// 分页信息类型
export interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

// 内容列表响应类型
export interface ContentListResponse<T> {
  items: T[]
  pagination: PaginationInfo
  filters: ContentFilter
  sort: ContentSortOption
}

// 产品规格类型
export interface ProductSpecs {
  [key: string]: string | number | boolean | ProductSpecs
}

// 产品尺寸类型
export interface ProductDimensions {
  height?: string
  width?: string
  length?: string
  weight?: string
  volume?: string
}

// 产品价格区间类型
export interface ProductPriceRange {
  min: number
  max: number
  currency: string
  unit?: string
}

// 产品图片画廊类型
export interface ProductGallery {
  main: string
  thumbnails: string[]
  videos?: string[]
  documents?: string[]
}

// 页面英雄区域类型
export interface PageHero {
  title: string
  subtitle?: string
  image?: string
  video?: string
  cta?: {
    text: string
    href: string
    variant?: 'primary' | 'secondary' | 'outline'
  }
}

// 页面区块类型
export interface PageSection {
  id: string
  type: 'hero' | 'features' | 'testimonials' | 'cta' | 'content' | 'gallery'
  title?: string
  content?: any
  settings?: {
    background?: string
    padding?: string
    alignment?: 'left' | 'center' | 'right'
  }
}

// 博客作者类型
export interface BlogAuthor {
  name: string
  avatar?: string
  bio?: string
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
  }
}

// 内容状态类型
export type ContentStatus = 'draft' | 'published' | 'archived'

// 内容可见性类型
export type ContentVisibility = 'public' | 'private' | 'password-protected'

// 多语言内容类型
export interface LocaleContent {
  locale: string
  title: string
  description: string
  content: string
  slug: string
  published: boolean
}

// 内容元数据类型
export interface ContentMetadata {
  id: string
  type: 'product' | 'page' | 'blog'
  locale: string
  slug: string
  title: string
  description: string
  publishedAt: string
  updatedAt?: string
  author?: string | BlogAuthor
  tags?: string[]
  category?: string
  featured?: boolean
  status: ContentStatus
  visibility: ContentVisibility
}

// 站点地图项类型
export interface SitemapItem {
  url: string
  lastModified: string
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

// 导出所有类型
export type {
  Product,
  Page,
  Blog,
}

// 内容工具函数类型
export interface ContentUtils {
  getProductsByCategory: (category: string, locale?: string) => Product[]
  getFeaturedProducts: (locale?: string) => Product[]
  getRelatedProducts: (product: Product, limit?: number) => Product[]
  getBlogPostsByCategory: (category: string, locale?: string) => Blog[]
  getFeaturedBlogPosts: (locale?: string) => Blog[]
  getPagesByType: (type: string, locale?: string) => Page[]
  searchContent: (query: string, filters?: ContentFilter) => SearchResult[]
  generateTableOfContents: (content: string) => TableOfContentsItem[]
  generateBreadcrumbs: (path: string, locale?: string) => Breadcrumb[]
}

'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'
import { SUPPORTED_LOCALES, type SupportedLocale, getLanguageSwitcherData } from '@/lib/i18n'

interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'inline'
  showFlag?: boolean
  showNativeName?: boolean
  className?: string
}

export default function LanguageSwitcher({
  variant = 'dropdown',
  showFlag = true,
  showNativeName = true,
  className = ''
}: LanguageSwitcherProps) {
  const { t } = useTranslation('common')
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  const currentLocale = (router.locale as SupportedLocale) || 'zh-CN'
  const languages = getLanguageSwitcherData(currentLocale)
  const currentLanguage = languages.find(lang => lang.active)

  // 处理语言切换
  const handleLanguageChange = (locale: SupportedLocale) => {
    const { pathname, asPath, query } = router
    router.push({ pathname, query }, asPath, { locale })
    setIsOpen(false)
  }

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 内联样式渲染
  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {languages.map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={`
              px-3 py-1 rounded-md text-sm font-medium transition-colors
              ${language.active 
                ? 'bg-primary-100 text-primary-700' 
                : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100'
              }
            `}
            aria-label={`Switch to ${language.name}`}
          >
            {showFlag && <span className="mr-1">{language.flag}</span>}
            {showNativeName ? language.nativeName : language.name}
          </button>
        ))}
      </div>
    )
  }

  // 下拉菜单样式渲染
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="
          flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium
          text-neutral-700 hover:text-neutral-900 hover:bg-neutral-100
          transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500
        "
        aria-label={t('navigation.language')}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {showFlag && currentLanguage && (
          <span className="text-lg">{currentLanguage.flag}</span>
        )}
        <span>
          {currentLanguage && (showNativeName ? currentLanguage.nativeName : currentLanguage.name)}
        </span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="
          absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5
          focus:outline-none z-50 animate-slide-down
        ">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`
                  w-full flex items-center px-4 py-2 text-sm transition-colors
                  ${language.active
                    ? 'bg-primary-50 text-primary-700'
                    : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
                  }
                `}
                role="menuitem"
                aria-label={`Switch to ${language.name}`}
              >
                {showFlag && (
                  <span className="text-lg mr-3">{language.flag}</span>
                )}
                <div className="flex flex-col items-start">
                  <span className="font-medium">
                    {showNativeName ? language.nativeName : language.name}
                  </span>
                  {showNativeName && language.nativeName !== language.name && (
                    <span className="text-xs text-neutral-500">{language.name}</span>
                  )}
                </div>
                {language.active && (
                  <svg
                    className="ml-auto w-4 h-4 text-primary-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// 简化版语言切换器（仅显示标志）
export function LanguageSwitcherCompact({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher
      variant="dropdown"
      showFlag={true}
      showNativeName={false}
      className={className}
    />
  )
}

// 移动端语言切换器
export function LanguageSwitcherMobile({ className = '' }: { className?: string }) {
  const { t } = useTranslation('common')
  const router = useRouter()
  const currentLocale = (router.locale as SupportedLocale) || 'zh-CN'
  const languages = getLanguageSwitcherData(currentLocale)

  const handleLanguageChange = (locale: SupportedLocale) => {
    const { pathname, asPath, query } = router
    router.push({ pathname, query }, asPath, { locale })
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h3 className="text-sm font-medium text-neutral-900 mb-3">
        {t('navigation.language')}
      </h3>
      {languages.map((language) => (
        <button
          key={language.code}
          onClick={() => handleLanguageChange(language.code)}
          className={`
            w-full flex items-center justify-between px-3 py-2 rounded-md text-sm
            transition-colors
            ${language.active
              ? 'bg-primary-100 text-primary-700'
              : 'text-neutral-700 hover:bg-neutral-100'
            }
          `}
        >
          <div className="flex items-center">
            <span className="text-lg mr-3">{language.flag}</span>
            <span>{language.nativeName}</span>
          </div>
          {language.active && (
            <svg
              className="w-4 h-4 text-primary-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </button>
      ))}
    </div>
  )
}

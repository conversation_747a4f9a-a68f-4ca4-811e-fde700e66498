import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: '<PERSON><PERSON><PERSON> - 专业防洪设备制造商',
    template: '%s | <PERSON><PERSON><PERSON>'
  },
  description: '图森博格(<PERSON><PERSON><PERSON>)成立于2010年，专注于防洪设备研发、设计与制造，为全球用户提供质量过硬的防洪产品系列和综合解决方案。',
  keywords: ['防洪设备', '防洪墙', '防洪产品', '应急防洪', '工程防洪', '<PERSON><PERSON><PERSON>', '图森博格'],
  authors: [{ name: '<PERSON><PERSON><PERSON>', url: 'https://tucsenberg.com' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'en': '/en',
      'zh-CN': '/zh-CN',
      'ja': '/ja',
      'es': '/es',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: 'Tucsenberg - 专业防洪设备制造商',
    description: '图森博格专注于防洪设备研发、设计与制造，为全球用户提供质量过硬的防洪产品系列和综合解决方案。',
    siteName: 'Tucsenberg',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Tucsenberg 防洪设备',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tucsenberg - 专业防洪设备制造商',
    description: '图森博格专注于防洪设备研发、设计与制造，为全球用户提供质量过硬的防洪产品系列和综合解决方案。',
    images: ['/images/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="msapplication-TileColor" content="#2563eb" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  )
}

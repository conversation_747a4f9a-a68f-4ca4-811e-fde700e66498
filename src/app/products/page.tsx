import type { Metadata } from 'next'
import { ProductUtils } from '@/lib/content'

export const metadata: Metadata = {
  title: '产品系列',
  description: '<PERSON><PERSON><PERSON>防洪设备产品系列，包括防洪墙、防洪闸、应急防洪等专业产品',
}

export default function ProductsPage() {
  // 获取所有产品
  const featuredProducts = ProductUtils.getFeatured('zh-CN', 10)
  const categories = ProductUtils.getCategories('zh-CN')
  
  return (
    <main className="min-h-screen py-20">
      <div className="container-custom">
        {/* 页面标题 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
            产品系列
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            专业的防洪设备产品线，满足不同场景的防洪需求，为您提供可靠的防护解决方案
          </p>
        </div>

        {/* 产品分类 */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-neutral-900 mb-6">产品分类</h2>
          <div className="flex flex-wrap gap-4">
            {categories.map((category) => (
              <div
                key={category}
                className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
              >
                {category}
              </div>
            ))}
          </div>
        </div>

        {/* 特色产品 */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-neutral-900 mb-8">特色产品</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <div key={product._id} className="card-hover">
                {/* 产品图片占位符 */}
                <div className="aspect-video bg-gradient-to-br from-primary-100 to-primary-200 rounded-t-xl flex items-center justify-center">
                  <div className="text-primary-600 text-2xl font-bold">
                    {product.title.split(' ')[0]}
                  </div>
                </div>
                
                {/* 产品信息 */}
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs px-2 py-1 bg-secondary-100 text-secondary-700 rounded">
                      {product.categoryLabel}
                    </span>
                    {product.featured && (
                      <span className="text-xs px-2 py-1 bg-accent-100 text-accent-700 rounded">
                        特色
                      </span>
                    )}
                    {product.isNew && (
                      <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded">
                        新品
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                    {product.title}
                  </h3>
                  
                  <p className="text-neutral-600 mb-4 text-ellipsis-3">
                    {product.description}
                  </p>
                  
                  {/* 产品规格 */}
                  {product.specs && (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {Object.entries(product.specs).slice(0, 4).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-neutral-500">{key}:</span>
                            <span className="text-neutral-700">{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* 标签 */}
                  {product.tags && product.tags.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {product.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="text-xs px-2 py-1 bg-neutral-100 text-neutral-600 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* 操作按钮 */}
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-neutral-500">
                      阅读时间: {product.readingTime}分钟
                    </div>
                    <button className="text-primary-600 hover:text-primary-700 font-medium">
                      了解更多 →
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-neutral-50 rounded-xl p-8">
          <h2 className="text-2xl font-semibold text-neutral-900 mb-6 text-center">
            产品统计
          </h2>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary-600 mb-2">
                {featuredProducts.length}
              </div>
              <div className="text-neutral-600">特色产品</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-secondary-600 mb-2">
                {categories.length}
              </div>
              <div className="text-neutral-600">产品分类</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-accent-600 mb-2">
                {featuredProducts.filter(p => p.isNew).length}
              </div>
              <div className="text-neutral-600">新品</div>
            </div>
          </div>
        </div>

        {/* 联系我们 */}
        <div className="text-center mt-16">
          <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
            需要定制化解决方案？
          </h2>
          <p className="text-neutral-600 mb-8">
            我们的专业团队可以为您提供定制化的防洪解决方案
          </p>
          <button className="btn-primary px-8 py-4 text-lg">
            联系我们
          </button>
        </div>
      </div>
    </main>
  )
}
